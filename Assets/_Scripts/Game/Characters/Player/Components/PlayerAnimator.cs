using UnityEngine;

/// <summary>
/// <summary>
/// This class is responsible for managing the player's animations.
/// It interfaces with the <see cref="Animator"/> to trigger animations based on the player's state.
/// </summary>
[RequireComponent(typeof(Animator), typeof(SpriteRenderer))]
public class PlayerAnimator : MonoBehaviour
{
    [Header("References")]
    [SerializeField]
    private Animator _anim;

    [SerializeField]
    private SpriteRenderer _sprite;

    [Header("Settings")]
    [SerializeField, Range(1f, 3f)]
    private float _maxIdleSpeed = 2;

    [SerializeField]
    private float _maxTilt = 5;

    [SerializeField]
    private float _tiltSpeed = 20;

    private IPlayerController _player;
    private bool _grounded;

    private void Awake()
    {
        _player = GetComponentInParent<IPlayerController>();
    }

    private void OnEnable()
    {
        _player.Jumped += OnJumped;
        _player.GroundedChanged += OnGroundedChanged;
    }

    private void OnDisable()
    {
        _player.Jumped -= OnJumped;
        _player.GroundedChanged -= OnGroundedChanged;
    }

    private void Update()
    {
        if (_player == null)
            return;

        HandleSpriteFlipping();
        HandleSwimmingAnimations();
        HandleIdleSpeed();
        HandleCharacterTilt();
    }

    private void HandleSpriteFlipping()
    {
        if (_player.FrameInput.x != 0)
        {
            _sprite.flipX = _player.FrameInput.x < 0;
        }
    }

    private void HandleSwimmingAnimations()
    {
        var isSwimming = _player.IsSwimming;
        _anim.SetBool("Swimming", isSwimming);

        if (isSwimming)
        {
            _anim.SetBool("Diving", _player.IsDiving);
            if (_player.FrameInput != Vector2.zero)
            {
                _sprite.flipX = _player.FrameInput.x < 0;
            }
        }
        else
        {
            _anim.SetBool("Diving", false);
        }
    }

    private void HandleIdleSpeed()
    {
        var inputStrength = Mathf.Abs(_player.FrameInput.x);
        _anim.SetFloat(IdleSpeedKey, Mathf.Lerp(1, _maxIdleSpeed, inputStrength));
    }

    private void HandleCharacterTilt()
    {
        if (!_grounded)
            return;

        var runningTilt = Quaternion.Euler(0, 0, _maxTilt * _player.FrameInput.x);
        _anim.transform.up = Vector3.RotateTowards(
            _anim.transform.up,
            runningTilt * Vector2.up,
            _tiltSpeed * Time.deltaTime,
            0f
        );
    }

    private void OnJumped()
    {
        _anim.SetTrigger(JumpKey);
        _anim.ResetTrigger(GroundedKey);
    }

    private void OnGroundedChanged(bool grounded, float impact)
    {
        _grounded = grounded;
        if (grounded)
        {
            _anim.SetTrigger(GroundedKey);
        }
    }

    private static readonly int GroundedKey = Animator.StringToHash("Grounded");
    private static readonly int IdleSpeedKey = Animator.StringToHash("IdleSpeed");
    private static readonly int JumpKey = Animator.StringToHash("Jump");
}

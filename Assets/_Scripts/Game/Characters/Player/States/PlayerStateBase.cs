using UnityEngine;

public abstract class PlayerStateBase
{
    public virtual bool ShouldResetRotationOnExit => true;
    protected PlayerController player;

    public PlayerStateBase(PlayerController player)
    {
        this.player = player;
    }

    public virtual void Enter() { }

    public virtual void Exit() { }

    public virtual void Update() { }

    public virtual void FixedUpdate() { }
}

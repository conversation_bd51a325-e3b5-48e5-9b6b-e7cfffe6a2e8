using UnityEngine;

public class PlayerLedgeGrabState : PlayerStateBase
{
    public PlayerLedgeGrabState(PlayerController player)
        : base(player) { }

    public override void Enter()
    {
        base.Enter();
        player.SetVelocity(Vector2.zero);
    }

    public override void Update()
    {
        CheckForTransitions();
    }

    private void CheckForTransitions()
    {
        if (player.Input.JumpDown)
        {
            // Set vertical velocity to JumpPower for a consistent jump from ledge
            player.SetVerticalVelocity(player.Stats.JumpPower);
            player.SwitchState(player.AirState);
            return;
        }
    }
}

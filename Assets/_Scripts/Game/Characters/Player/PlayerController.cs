using System;
using UnityEngine;

[RequireComponent(typeof(Rigidbody2D), typeof(Collider2D))]
public class PlayerController : Mono<PERSON><PERSON><PERSON><PERSON>, IPlayerController
{
    #region Events

    public event Action<bool, float> GroundedChanged;
    public event Action Jumped;

    #endregion

    #region Components & Dependencies

    [SerializeField]
    private PlayerScriptableStats _stats;
    public PlayerScriptableStats Stats => _stats;

    public Rigidbody2D RB { get; private set; }
    public CapsuleCollider2D Col { get; private set; }

    #endregion

    #region State Machine

    public PlayerStateBase CurrentState { get; private set; }
    public PlayerGroundState GroundState { get; private set; }
    public PlayerAirState AirState { get; private set; }
    public PlayerWallSlideState WallSlideState { get; private set; }
    public PlayerLedgeGrabState LedgeGrabState { get; private set; }
    public PlayerSwimState SwimState { get; private set; }
    public PlayerRopeClimbState ClimbState { get; private set; }
    public PlayerDashState DashState { get; private set; }

    #endregion

    #region IPlayerController Properties

    public bool IsSwimming => CurrentState is PlayerSwimState;
    public bool IsDiving { get; set; }
    public bool IsClimbing => CurrentState is PlayerRopeClimbState;
    public bool IsDashing => CurrentState is PlayerDashState;
    public bool IsCrouching { get; set; }
    public bool IsGliding { get; set; }
    public bool IsWallSliding => CurrentState is PlayerWallSlideState;
    public bool IsTouchingWall { get; private set; }
    public bool IsTouchingClimbableWall { get; private set; }
    public float WallSide { get; private set; }
    public bool IsFacingRight => LastFacing > 0;
    public bool CanControl { get; set; } = true;
    public Vector2 Speed => RB.linearVelocity;
    public Vector2 FrameInput => _frameInput.Move;
    public bool IsSubmerged
    {
        get
        {
            if (WaterCollider == null)
                return false;
            // Hysteresis to prevent jittering at the surface
            const float margin = 0.05f;
            return Col.bounds.min.y < WaterCollider.bounds.max.y - margin;
        }
    }

    #endregion

    #region Public Properties

    public FrameInput Input => _frameInput;
    public Vector2 FrameVelocity { get; set; }
    public float LastFacing { get; set; } = 1f;
    public float OrigColHeight { get; private set; }
    public Vector2 OrigColOffset { get; private set; }
    public Collider2D WaterCollider { get; set; }
    public Rope CurrentRope { get; set; }
    public RopeDownInteractionZone CurrentRopeDownZone { get; set; }
    public float Time { get; private set; }
    public float FrameLeftGrounded { get; set; } = float.MinValue;
    public bool WantsToClimb { get; private set; }

    #endregion

    #region Private Fields

    private FrameInput _frameInput;
    private bool _cachedQueryStartInColliders;
    private bool _grounded;
    private bool _isNearLedge;

    #endregion

    #region Unity Lifecycle

    private void Awake()
    {
        RB = GetComponent<Rigidbody2D>();
        Col = GetComponent<CapsuleCollider2D>();

        OrigColHeight = Col.size.y;
        OrigColOffset = Col.offset;

        _cachedQueryStartInColliders = Physics2D.queriesStartInColliders;

        GroundState = new PlayerGroundState(this);
        AirState = new PlayerAirState(this);
        WallSlideState = new PlayerWallSlideState(this);
        LedgeGrabState = new PlayerLedgeGrabState(this);
        SwimState = new PlayerSwimState(this);
        ClimbState = new PlayerRopeClimbState(this);
        DashState = new PlayerDashState(this);

        SwitchState(GroundState);
    }

    private void Update()
    {
        Time += UnityEngine.Time.deltaTime;
        GatherInput();

        CurrentState?.Update();
        Debug.Log(CurrentState.GetType().Name);
    }

    private void FixedUpdate()
    {
        CheckCollisions();
        CurrentState?.FixedUpdate();
    }

    private void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Water"))
            WaterCollider = other;
        else if (other.CompareTag("BouncePad") && RB.linearVelocity.y <= -0.1f)
        {
            ExecuteBouncePad(other.GetComponent<BouncePad>());
        }
    }

    private void OnTriggerExit2D(Collider2D other)
    {
        if (other.CompareTag("Water") && WaterCollider == other)
            WaterCollider = null;
    }

    #endregion

    #region State Machine

    public void SwitchState(PlayerStateBase newState)
    {
        if (CurrentState != null)
        {
            if (CurrentState.ShouldResetRotationOnExit)
            {
                RB.rotation = 0;
            }
            CurrentState.Exit();
        }

        CurrentState = newState;
        CurrentState.Enter();
    }

    #endregion

    #region Input Handling

    private void GatherInput()
    {
        _frameInput = new FrameInput
        {
            JumpDown = UnityEngine.Input.GetButtonDown("Jump"),
            JumpHeld = UnityEngine.Input.GetButton("Jump"),
            Move = new Vector2(
                UnityEngine.Input.GetAxisRaw("Horizontal"),
                UnityEngine.Input.GetAxisRaw("Vertical")
            ),
            DashDown = UnityEngine.Input.GetKeyDown(KeyCode.C),
            CrouchHeld = UnityEngine.Input.GetKey(KeyCode.X),
            GlideHeld = UnityEngine.Input.GetKey(KeyCode.G),
            InteractDown = UnityEngine.Input.GetKeyDown(KeyCode.E),
        };

        if (_stats.SnapInput)
        {
            _frameInput.Move.x =
                Mathf.Abs(_frameInput.Move.x) < _stats.HorizontalDeadZoneThreshold
                    ? 0
                    : Mathf.Sign(_frameInput.Move.x);
            _frameInput.Move.y =
                Mathf.Abs(_frameInput.Move.y) < _stats.VerticalDeadZoneThreshold
                    ? 0
                    : Mathf.Sign(_frameInput.Move.y);
        }

        if (_frameInput.Move.x != 0)
            LastFacing = _frameInput.Move.x;
    }

    #endregion

    #region Collision Detection

    public bool IsGrounded() => _grounded;

    public bool CheckForLedge()
    {
        if (IsGrounded())
            return false;

        var hit = Physics2D.Raycast(
            Col.bounds.center + new Vector3(LastFacing * 0.25f, Col.bounds.extents.y, 0),
            Vector2.down,
            0.25f,
            ~_stats.PlayerLayer
        );

        _isNearLedge = hit.collider != null;
        return _isNearLedge;
    }

    private void CheckCollisions()
    {
        Physics2D.queriesStartInColliders = false;

        // Wall
        RaycastHit2D wallHit = Physics2D.CapsuleCast(
            Col.bounds.center,
            Col.size,
            Col.direction,
            0,
            new Vector2(LastFacing, 0),
            _stats.GrounderDistance,
            ~_stats.PlayerLayer
        );
        IsTouchingWall = wallHit;
        IsTouchingClimbableWall =
            wallHit.collider != null && wallHit.collider.GetComponent<Climbable>() != null;
        if (IsTouchingWall)
            WallSide = Mathf.Sign(wallHit.normal.x);

        // Ground
        bool groundHit =
            !IsSwimming
            && Physics2D.CapsuleCast(
                Col.bounds.center,
                Col.size,
                Col.direction,
                0,
                Vector2.down,
                _stats.GrounderDistance,
                ~(_stats.PlayerLayer | _stats.WaterLayer)
            );

        // Ceiling
        bool ceilingHit = Physics2D.CapsuleCast(
            Col.bounds.center,
            Col.size,
            Col.direction,
            0,
            Vector2.up,
            _stats.GrounderDistance,
            ~(_stats.PlayerLayer | _stats.WaterLayer)
        );
        if (ceilingHit)
            FrameVelocity = new Vector2(FrameVelocity.x, Mathf.Min(0, FrameVelocity.y));

        // Landed
        if (!_grounded && groundHit)
        {
            _grounded = true;
            GroundedChanged?.Invoke(true, Mathf.Abs(FrameVelocity.y));
        }
        // Left Ground
        else if (_grounded && !groundHit)
        {
            _grounded = false;
            FrameLeftGrounded = Time;
            GroundedChanged?.Invoke(false, 0);
        }

        Physics2D.queriesStartInColliders = _cachedQueryStartInColliders;
    }

    #endregion

    #region Movement & Physics

    public void ApplyMovement() => RB.linearVelocity = FrameVelocity;

    public void ApplyVelocity(Vector2 vel) => FrameVelocity = vel;

    public void SetVelocity(Vector2 velocity) => RB.linearVelocity = velocity;

    public void SetVerticalVelocity(float y) =>
        RB.linearVelocity = new Vector2(RB.linearVelocity.x, y);

    #endregion

    #region Interaction

    public void OnEnterRopeDownZone(RopeDownInteractionZone zone) => CurrentRopeDownZone = zone;

    public void OnExitRopeDownZone(RopeDownInteractionZone zone)
    {
        if (CurrentRopeDownZone == zone)
            CurrentRopeDownZone = null;
    }

    public void ExecuteBouncePad(BouncePad pad)
    {
        if (pad == null || !pad.CanBounce)
            return;
        FrameVelocity = new Vector2(FrameVelocity.x, pad.BounceForce);
        pad.TriggerEffects();
    }

    #endregion

    #region Climbing

    public bool IsClimbingOnRope(Rope rope) => IsClimbing && CurrentRope == rope;

    public void SetClimbing(bool climbing, Rope rope = null)
    {
        WantsToClimb = climbing;
        if (climbing)
            CurrentRope = rope;
    }

    public void StopClimbing() => WantsToClimb = false;

    public void ExitRope(Rope rope)
    {
        if (CurrentState is PlayerRopeClimbState climbState)
        {
            climbState.ExitRope(rope);
        }
    }

    #endregion

    #region Miscellaneous

    public void InvokeJumped() => Jumped?.Invoke();

    #endregion

#if UNITY_EDITOR
    #region Editor Only
    public void OnValidate()
    {
        if (_stats == null)
            Debug.LogWarning(
                "Please assign a ScriptableStats asset to the Player Controller's Stats slot",
                this
            );
    }

    private void OnDrawGizmos()
    {
        if (Col == null)
            return;
        Gizmos.color = Color.cyan;
        var center = Col.bounds.center;
        Gizmos.DrawLine(center + Vector3.left * 0.2f, center + Vector3.right * 0.2f);
    }
    #endregion
#endif
}

using UnityEngine;

public abstract class WeaponBase : MonoBeh<PERSON><PERSON>
{
    /// <summary>Called when the player begins pulling back the weapon (e.g. mouse down).</summary>
    public abstract void PullStart();

    /// <summary>Called each frame while the player is holding pull (e.g. mouse held).</summary>
    public abstract void PullUpdate();

    /// <summary>Called when the player releases to fire the weapon.</summary>
    public abstract void ReleaseFire();
}

using System.Collections;
using UnityEngine;

[RequireComponent(typeof(Collider2D))]
public class Rope : MonoBehaviour
{
    public RopeDirection Direction = RopeDirection.Both;
    public Vector2 ExitForce = new(5, 5);

    [Tooltip("The y-offset from the top of the rope's collider to set the exit point.")]
    public float ExitPointYOffset = 0.5f;
    public Vector2 PlayerExitPosition = new(0, 1);

    [Header("Latching")]
    public Vector2 TopLatchPointOffset = new(0, -1);

    private Collider2D _collider;
    private bool _isOnCooldown;

    public Vector2 WorldPlayerExitPoint => (Vector2)transform.position + PlayerExitPosition;

    public Vector2 TopLatchPoint => (Vector2)transform.position + TopLatchPointOffset;

    private void Awake()
    {
        _collider = GetComponent<Collider2D>();
    }

    public void StartCooldown(float duration)
    {
        StartCoroutine(Cooldown(duration));
    }

    private void OnTriggerEnter2D(Collider2D collision)
    {
        if (_isOnCooldown)
            return;

        var player = collision.GetComponent<PlayerController>();
        if (player == null || player.Stats == null)
            return;

        if (collision.CompareTag(player.Stats.PlayerTag))
        {
            player.SetClimbing(true, this);
        }
    }

    private void OnTriggerExit2D(Collider2D collision)
    {
        var player = collision.GetComponent<PlayerController>();
        if (player == null)
            return;

        if (collision.CompareTag(player.Stats.PlayerTag))
        {
            player.SetClimbing(false, null);
        }
    }

    private IEnumerator Cooldown(float duration)
    {
        _isOnCooldown = true;
        yield return new WaitForSeconds(duration);
        _isOnCooldown = false;
    }

    private void OnDrawGizmos()
    {
        if (_collider == null)
            _collider = GetComponent<Collider2D>();

        var bounds = _collider.bounds;

        // Draw top and bottom bounds
        Gizmos.color = Color.red;
        var topPoint = new Vector2(bounds.center.x, bounds.max.y);
        var bottomPoint = new Vector2(bounds.center.x, bounds.min.y);
        Gizmos.DrawLine(topPoint + Vector2.left * 0.5f, topPoint + Vector2.right * 0.5f);
        Gizmos.DrawLine(bottomPoint + Vector2.left * 0.5f, bottomPoint + Vector2.right * 0.5f);

        // Draw exit point
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(WorldPlayerExitPoint, 0.2f);
    }
}

using UnityEngine;

public class PlayerWallSlideState : PlayerStateBase
{
    private readonly PlayerController _player;
    private readonly PlayerScriptableStats _stats;
    private FrameInput _frameInput;
    private float _wallStickTimer;
    private float _jumpTimer;

    public PlayerWallSlideState(PlayerController player)
        : base(player)
    {
        _player = player;
        _stats = player.Stats;
    }

    public override void Enter()
    {
        base.Enter();
        _wallStickTimer = _stats.WallStickiness;
        _jumpTimer = _stats.WallJumpTimer;
    }

    public override void Exit()
    {
        base.Exit();
    }

    public override void Update()
    {
        _frameInput = _player.Input;
        _jumpTimer -= Time.deltaTime;
        _wallStickTimer -= Time.deltaTime;

        // Clamp vertical speed
        if (_player.Speed.y < -_stats.WallSlideSpeed)
        {
            _player.SetVerticalVelocity(-_stats.WallSlideSpeed);
        }

        // Handle wall sticking
        if (_wallStickTimer > 0)
        {
            _player.SetVerticalVelocity(-_stats.WallSlideSpeed / 2);
        }

        CheckForTransitions();
    }

    private void CheckForTransitions()
    {
        // State Transitions
        if (_frameInput.JumpDown && _jumpTimer > 0)
        {
            var jumpForce = new Vector2(
                _stats.WallJumpPower.x * -_player.WallSide,
                _stats.WallJumpPower.y
            );
            _player.SetVelocity(jumpForce);
            _player.SwitchState(_player.AirState);
            return;
        }

        if (_player.IsSubmerged)
        {
            _player.SwitchState(_player.SwimState);
            return;
        }

        if (!_player.IsTouchingClimbableWall || _player.IsGrounded())
        {
            _player.SwitchState(_player.AirState);
            return;
        }
    }

    public override void FixedUpdate()
    {
        base.FixedUpdate();
    }
}

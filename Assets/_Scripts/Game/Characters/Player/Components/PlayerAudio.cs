using UnityEngine;

/// <summary>
/// Handles the audio effects for the player, such as footsteps.
/// </summary>
[RequireComponent(typeof(AudioSource), typeof(IPlayerController))]
public class PlayerAudio : MonoBehaviour
{
    [Header("Audio Clips")]
    [SerializeField]
    private AudioClip[] _footsteps;

    private AudioSource _source;
    private IPlayerController _player;

    private void Awake()
    {
        _source = GetComponent<AudioSource>();
        _player = GetComponent<IPlayerController>();
    }

    private void OnEnable()
    {
        _player.GroundedChanged += OnGroundedChanged;
    }

    private void OnDisable()
    {
        _player.GroundedChanged -= OnGroundedChanged;
    }

    private void OnGroundedChanged(bool grounded, float impact)
    {
        if (grounded)
        {
            _source.PlayOneShot(_footsteps[Random.Range(0, _footsteps.Length)]);
        }
    }
}

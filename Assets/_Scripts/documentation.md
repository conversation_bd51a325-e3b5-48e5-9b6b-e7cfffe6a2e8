# Scripts

This folder contains all the scripts for the game, organized into subfolders based on their functionality.

## Contents Summary

*   **Core:** Contains the core systems of the game, such as attributes and scriptable objects.
*   **Editor:** Contains editor scripts for custom inspectors and tools.
*   **Game:** Contains the main gameplay logic, including characters, combat, interactions, items, and world elements.
*   **Systems:** Contains the game's systems, such as audio, events, input, save/load, scene management, and the state machine.
*   **UI:** Contains the UI scripts.
*   **Utilities:** Contains utility scripts that can be used throughout the project.

## Usage

To use a script, simply attach it to a GameObject in the scene. Some scripts may require other components to be attached to the same GameObject.

## Important Notes

*   Please follow the coding guidelines when creating new scripts or modifying existing ones.
*   If you make any changes to the codebase, please update this documentation file accordingly.

## Dependencies

*   None

---

# Game/Characters/Player

This folder contains the player character's scripts.

## Contents Summary

*   **PlayerController.cs:** The central component for managing player behavior, including movement, state transitions, and interactions. It implements a state machine to handle various player states like walking, jumping, swimming, and climbing.

## Usage

Attach the `PlayerController.cs` script to the player's GameObject. This script requires a `Rigidbody2D` and a `CapsuleCollider2D` to function correctly. It also requires a `PlayerScriptableStats` asset to be assigned in the Inspector.

## Important Notes

*   The script has been recently refactored to improve readability and organization. Properties, fields, and methods are now grouped into logical regions, such as `Events`, `State Machine`, `IPlayerController Properties`, and `Collision Detection`. This makes the code easier to navigate and maintain.
*   The logic for handling rope down interaction has been moved from the `PlayerController` to the `PlayerGroundState` and `PlayerAirState`.

## Dependencies

*   `PlayerScriptableStats.cs`
*   `PlayerStateBase.cs` and its derived states
*   `IPlayerController.cs`
*   `Rope.cs`
*   `RopeDownInteractionZone.cs`
*   `Climbable.cs`
*   `BouncePad.cs`

---

# Game/Characters/Player/States

This folder contains the different states that the player can be in.

## Contents Summary

*   **PlayerRopeClimbState.cs:** In this state, the player can climb up and down the rope. The player's movement is directly controlled, and gravity is disabled to ensure immediate stopping. The player will transition to the air state if they climb below the rope.
*   **PlayerGroundState.cs:** This state now handles the transition to the `PlayerRopeClimbState` when the player is in a `RopeDownInteractionZone` and presses the down key.
*   **PlayerAirState.cs:** This state now handles the transition to the `PlayerRopeClimbState` when the player is in a `RopeDownInteractionZone` and presses the down key.

## Usage

The player will enter this state when they come into contact with a rope.

## Important Notes

*   None

## Dependencies

*   `PlayerController.cs`
*   `Rope.cs`

---

# Game/Interactions/Environment/Rope

This folder contains scripts for rope interactions.

## Contents Summary

*   **Rope.cs:** Defines the properties of a rope, such as its segments and latch points.
*   **RopeDownInteractionZone.cs:** Manages the player's interaction with a rope. It notifies the `PlayerController` when the player enters or exits the trigger zone.
*   **RopeDirection.cs:** An enum to define the direction of the rope.
*   **RopeExitZone.cs:** Defines a zone where the player can exit a rope.

## Usage

To create a rope, attach the `Rope.cs` script to a GameObject. Then, create a child GameObject with a `Collider2D` set as a trigger and attach the `RopeDownInteractionZone.cs` script to it. Assign the `Rope.cs` script to the `Rope` field in the `RopeDownInteractionZone.cs` script.

## Important Notes

*   The player will latch onto the rope when they are in the interaction zone and press the down key.

## Dependencies

*   `PlayerController.cs`
*   `PlayerAirState.cs`
*   `PlayerRopeClimbState.cs`

---

# Game/Interactions/Environment

This folder contains scripts for environmental interactions.

## Contents Summary

*   **Climbable.cs:** A marker component for surfaces that the player can climb or wall-jump on.

## Usage

Attach the `Climbable` component to any GameObject that you want the player to be able to wall-slide or wall-jump on.

## Important Notes

*   This script is intentionally left empty. It is used as a tag to identify climbable surfaces.

## Dependencies

*   None

using UnityEngine;

public class PlayerSwimState : PlayerStateBase
{
    private float _timeStationary;
    private float _waterExitGraceTime;
    private const float WATER_SURFACE_HYSTERESIS = 0.05f;

    private float _prevRotation = 90f;

    public PlayerSwimState(PlayerController player)
        : base(player) { }

    public override void Enter()
    {
        // Reset velocity only if entering from a non-water state
        if (player.CurrentState is not PlayerSwimState)
        {
            player.FrameVelocity = Vector2.zero;
        }
        _timeStationary = 0f;
        _waterExitGraceTime = 0;
    }

    public override void Exit()
    {
        if (player.IsDiving)
        {
            ResetCollider();
        }
    }

    public override void FixedUpdate()
    {
        if (_waterExitGraceTime > 0)
        {
            _waterExitGraceTime -= Time.fixedDeltaTime;
            return;
        }

        CheckForTransitions();

        if (!player.CanControl)
            return;

        HandleSubmerging();
        HandleSwimming();
        HandleRotation();

        player.ApplyMovement();
    }

    #region Diving / Collider Adjustments

    private void ResetCollider()
    {
        player.IsDiving = false;
        player.Col.size = new Vector2(player.Col.size.x, player.OrigColHeight);
        player.Col.offset = player.OrigColOffset;
    }

    private void HandleSubmerging()
    {
        bool wantDive = player.Input.CrouchHeld;

        if (wantDive && !player.IsDiving)
        {
            player.IsDiving = true;
            float newHeight = Mathf.Max(
                0.1f,
                player.OrigColHeight - player.Stats.CrouchHeightReduction
            );
            player.Col.size = new Vector2(player.Col.size.x, newHeight);
            player.Col.offset =
                player.OrigColOffset - new Vector2(0, player.Stats.CrouchHeightReduction / 2f);
        }
        else if (!wantDive && player.IsDiving)
        {
            ResetCollider();
        }
    }

    #endregion

    #region Swimming Movement

    private void HandleSwimming()
    {
        if (player.WaterCollider == null)
            return;

        Vector2 moveInput = player.Input.Move;
        float waterSurfaceY = player.WaterCollider.bounds.max.y + player.Col.size.y / 2f; // half submerged feel
        float playerTopY = player.Col.bounds.max.y;

        // Calculate desired velocity from input
        Vector2 targetVelocity = moveInput.normalized * player.Stats.SwimSpeed;

        // Apply buoyancy when not moving vertically
        if (Mathf.Abs(moveInput.y) < 0.1f && playerTopY < waterSurfaceY - WATER_SURFACE_HYSTERESIS)
        {
            targetVelocity.y = Mathf.MoveTowards(
                player.FrameVelocity.y, // Use current velocity for smooth transition
                player.Stats.Buoyancy,
                player.Stats.FallAcceleration * Time.fixedDeltaTime
            );
        }

        // Smooth acceleration/deceleration
        player.FrameVelocity = Vector2.Lerp(
            player.FrameVelocity,
            targetVelocity,
            player.Stats.SwimAcceleration * Time.fixedDeltaTime
        );

        // Prevent upward motion past the surface
        if (playerTopY >= waterSurfaceY && player.FrameVelocity.y > 0f)
        {
            player.FrameVelocity = new Vector2(player.FrameVelocity.x, 0f);
        }
    }

    #endregion

    #region Rotation

    private void HandleRotation()
    {
        float targetAngle;

        // When moving horizontally, lock the rotation to a horizontal angle
        if (Mathf.Abs(player.Input.Move.x) > 0.1f)
        {
            targetAngle = -90f * Mathf.Sign(player.Input.Move.x);
            _prevRotation = targetAngle;
        }
        // Otherwise, rotate based on the full velocity vector (for diving/surfacing)
        else if (player.FrameVelocity.magnitude > 0.1f)
        {
            targetAngle =
                Mathf.Atan2(player.FrameVelocity.y, player.FrameVelocity.x) * Mathf.Rad2Deg - 90f;
            _timeStationary = 0f;
            _prevRotation = targetAngle;
        }
        // When stationary, maintain the last known rotation
        else
        {
            targetAngle = _prevRotation;
        }

        float smoothedAngle = Mathf.LerpAngle(
            player.RB.rotation,
            targetAngle,
            player.Stats.SwimRotationSpeed * Time.fixedDeltaTime
        );

        player.RB.MoveRotation(smoothedAngle);
    }

    #endregion

    #region Water Jump

    private void CheckForTransitions()
    {
        // Exit swim state if no longer submerged
        if (!player.IsSubmerged)
        {
            if (player.IsGrounded())
            {
                player.SwitchState(player.GroundState);
            }
            else
            {
                player.SwitchState(player.AirState);
            }
            return;
        }

        // Jumping out of water
        if (player.Input.JumpDown)
        {
            HandleJumping();
            return;
        }
    }

    private void HandleJumping()
    {
        if (player.IsDiving)
            return;

        player.FrameVelocity = new Vector2(
            player.Input.Move.x * player.Stats.SwimSpeed * player.Stats.WaterJumpLedgeClearance,
            player.Stats.WaterJumpPower
        );

        _waterExitGraceTime = 0.2f; // Grace period to prevent re-entry
        player.SwitchState(player.AirState);
    }

    #endregion
}

using UnityEngine;

// Interaction zone for latching at the top of the rope (should be placed on a platform where a rope is hanging down to latch to the top of it)
[RequireComponent(typeof(Collider2D))]
public class RopeDownInteractionZone : MonoBehaviour
{
    [SerializeField]
    private Rope _rope;

    public Rope Rope => _rope;

    private void Awake()
    {
        // Ensure the collider is a trigger
        GetComponent<Collider2D>().isTrigger = true;
    }

    private void OnTriggerEnter2D(Collider2D collision)
    {
        var player = collision.GetComponent<PlayerController>();
        if (player != null)
        {
            player.OnEnterRopeDownZone(this);
        }
    }

    private void OnTriggerExit2D(Collider2D collision)
    {
        var player = collision.GetComponent<PlayerController>();
        if (player != null)
        {
            player.OnExitRopeDownZone(this);
        }
    }

    public Vector2 GetLatchPoint()
    {
        if (_rope == null)
        {
            Debug.LogError("Rope is not assigned in the RopeDownInteractionZone.", this);
            return transform.position;
        }

        return _rope.TopLatchPoint;
    }

    private void OnDrawGizmos()
    {
        if (_rope == null)
            return;

        Gizmos.color = Color.cyan;
        Gizmos.DrawWireSphere(GetLatchPoint(), 0.2f);
    }
}

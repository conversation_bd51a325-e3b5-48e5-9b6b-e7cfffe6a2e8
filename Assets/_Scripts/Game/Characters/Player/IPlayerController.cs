using System;
using UnityEngine;

public interface IPlayerController
{
    public event Action<bool, float> GroundedChanged;
    public event Action Jumped;
    public Vector2 FrameInput { get; }
    public bool IsSwimming { get; }
    public bool IsDiving { get; }
    public bool IsWallSliding { get; }
    public bool IsTouchingWall { get; }
    public bool IsTouchingClimbableWall { get; }
    public bool IsFacingRight { get; }
    public Vector2 Speed { get; }
    public PlayerScriptableStats Stats { get; }

    public void SetVelocity(Vector2 velocity);
    public void SetVerticalVelocity(float y);
    public void SwitchState(PlayerStateBase state);
}

using UnityEngine;

public class PlayerAirState : PlayerStateBase
{
    #region Jumping

    private bool _jumpToConsume;
    private bool _bufferedJumpUsable;
    private bool _endedJumpEarly;
    private bool _coyoteUsable;
    private float _timeJumpWasPressed;
    public float WallStickGraceTime { get; set; }

    private bool HasBufferedJump =>
        _bufferedJumpUsable && player.Time < _timeJumpWasPressed + player.Stats.JumpBuffer;
    private bool CanUseCoyote =>
        _coyoteUsable
        && !player.IsGrounded()
        && player.Time < player.FrameLeftGrounded + player.Stats.CoyoteTime;

    #endregion

    public PlayerAirState(PlayerController player)
        : base(player) { }

    public override void Enter()
    {
        _coyoteUsable = true;
        _bufferedJumpUsable = true;
        _endedJumpEarly = false;
        WallStickGraceTime = 0;
    }

    public override void Update()
    {
        if (player.Input.JumpDown)
        {
            _jumpToConsume = true;
            _timeJumpWasPressed = player.Time;
        }
    }

    public override void FixedUpdate()
    {
        if (WallStickGraceTime > 0)
        {
            WallStickGraceTime -= Time.fixedDeltaTime;
        }

        HandleJump();
        HandleDirection();
        HandleGravity();
        player.ApplyMovement();

        CheckForTransitions();
    }

    private void CheckForTransitions()
    {
        if (player.IsGrounded())
        {
            player.SwitchState(player.GroundState);
            return;
        }

        if (player.IsSubmerged)
        {
            player.SwitchState(player.SwimState);
            return;
        }

        if (player.IsTouchingClimbableWall && player.FrameVelocity.y < 0 && WallStickGraceTime <= 0)
        {
            player.SwitchState(player.WallSlideState);
            return;
        }

        if (player.WantsToClimb)
        {
            player.SwitchState(player.ClimbState);
            return;
        }

        if (player.CurrentRopeDownZone != null && player.Input.Move.y < 0)
        {
            player.transform.position = player.CurrentRopeDownZone.GetLatchPoint();
            player.SetClimbing(true, player.CurrentRopeDownZone.Rope);
            player.SwitchState(player.ClimbState);
            return;
        }

        if (player.CheckForLedge() && player.CurrentRope == null)
        {
            player.SwitchState(player.LedgeGrabState);
            return;
        }
    }

    private void HandleJump()
    {
        if (
            !_endedJumpEarly
            && !player.IsGrounded()
            && !player.Input.JumpHeld
            && player.RB.linearVelocity.y > 0
        )
        {
            _endedJumpEarly = true;
        }

        if (_jumpToConsume || HasBufferedJump)
        {
            if (CanUseCoyote)
                ExecuteJump();
        }

        _jumpToConsume = false;
    }

    private void ExecuteJump()
    {
        _endedJumpEarly = false;
        _timeJumpWasPressed = 0;
        _bufferedJumpUsable = false;
        _coyoteUsable = false;
        player.FrameVelocity = new Vector2(player.FrameVelocity.x, player.Stats.JumpPower);
        player.InvokeJumped();
    }

    private void HandleDirection()
    {
        if (player.Input.Move.x == 0)
        {
            var deceleration = player.IsGrounded()
                ? player.Stats.GroundDeceleration
                : player.Stats.AirDeceleration;
            player.FrameVelocity = new Vector2(
                Mathf.MoveTowards(player.FrameVelocity.x, 0, deceleration * Time.fixedDeltaTime),
                player.FrameVelocity.y
            );
        }
        else
        {
            player.FrameVelocity = new Vector2(
                Mathf.MoveTowards(
                    player.FrameVelocity.x,
                    player.Input.Move.x * player.Stats.MaxSpeed,
                    player.Stats.Acceleration * Time.fixedDeltaTime
                ),
                player.FrameVelocity.y
            );
        }
    }

    private void HandleGravity()
    {
        if (player.Input.GlideHeld && player.FrameVelocity.y < 0)
        {
            HandleGlide();
        }
        else
        {
            player.IsGliding = false;
            var inAirGravity = player.Stats.FallAcceleration;
            if (_endedJumpEarly && player.FrameVelocity.y > 0)
                inAirGravity *= player.Stats.JumpEndEarlyGravityModifier;

            player.FrameVelocity = new Vector2(
                player.FrameVelocity.x,
                Mathf.MoveTowards(
                    player.FrameVelocity.y,
                    -player.Stats.MaxFallSpeed,
                    inAirGravity * Time.fixedDeltaTime
                )
            );
        }
    }

    private void HandleGlide()
    {
        player.IsGliding = true;

        // Horizontal movement during glide
        player.FrameVelocity = new Vector2(
            Mathf.MoveTowards(
                player.FrameVelocity.x,
                player.LastFacing * player.Stats.GlideForwardSpeed,
                player.Stats.Acceleration * Time.fixedDeltaTime
            ),
            player.FrameVelocity.y
        );

        // Vertical movement (falling) during glide
        player.FrameVelocity = new Vector2(
            player.FrameVelocity.x,
            Mathf.MoveTowards(
                player.FrameVelocity.y,
                -player.Stats.GlideFallSpeed,
                player.Stats.FallAcceleration * Time.fixedDeltaTime
            )
        );
    }
}

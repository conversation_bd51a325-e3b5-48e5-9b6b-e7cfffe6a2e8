using System;
using UnityEngine;

[RequireComponent(typeof(Rigidbody2D), typeof(Collider2D))]
public class PlayerController : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPlayerController
{
    [SerializeField]
    private PlayerScriptableStats _stats;
    private Rigidbody2D _rb;
    private CapsuleCollider2D _col;
    private FrameInput _frameInput;
    private Vector2 _frameVelocity;
    private bool _cachedQueryStartInColliders;

    private bool _dashToConsume;
    private bool _isDashing;
    private float _dashTimeLeft;
    private Vector2 _dashDirection;
    private float _dashTraveled;
    public float _lastFacing = 1f;

    private bool _isCrouching;
    private bool _isSwimming;
    private bool _isDiving;
    private bool _isSubmerged;
    private float _origColHeight;
    private Vector2 _origColOffset;
    private Collider2D _waterCollider;

    // NEW: Gliding state
    private bool _isGliding;

    // NEW: Climbing state
    private bool _isClimbing;
    private Rope _currentRope;
    private RopeDownInteractionZone _currentRopeZone;

    // IPlayerController implementation
    public bool IsSwimming => _isSwimming;
    public bool IsDiving => _isDiving;
    public bool IsWallSliding => false; // Not implemented in this version
    public bool IsTouchingWall => false; // Not implemented in this version
    public bool IsTouchingClimbableWall => false; // Not implemented in this version
    public bool IsFacingRight => _lastFacing > 0;
    public Vector2 Speed => _rb.linearVelocity;
    public PlayerScriptableStats Stats => _stats;

    public bool IsClimbingOnRope(Rope rope) => _isClimbing && _currentRope == rope;

    #region Interface

    public Vector2 FrameInput => _frameInput.Move;
    public event Action<bool, float> GroundedChanged;
    public event Action Jumped;

    #endregion

    private float _time;

    private void Awake()
    {
        _rb = GetComponent<Rigidbody2D>();
        _col = GetComponent<CapsuleCollider2D>();
        // Cache original collider dimensions for crouch
        _origColHeight = _col.size.y;
        _origColOffset = _col.offset;

        _cachedQueryStartInColliders = Physics2D.queriesStartInColliders;
    }

    private void Update()
    {
        _time += UnityEngine.Time.deltaTime;
        GatherInput();
        HandleRopeLatching();
    }

    private void GatherInput()
    {
        _frameInput = new FrameInput
        {
            JumpDown = UnityEngine.Input.GetButtonDown("Jump"),
            JumpHeld = UnityEngine.Input.GetButton("Jump"),
            Move = new Vector2(UnityEngine.Input.GetAxisRaw("Horizontal"), UnityEngine.Input.GetAxisRaw("Vertical")),
            DashDown = UnityEngine.Input.GetKeyDown(KeyCode.C),
            CrouchHeld = UnityEngine.Input.GetKey(KeyCode.X),
            // NEW: Added Glide input
            GlideHeld = UnityEngine.Input.GetKey(KeyCode.G),
            InteractDown = UnityEngine.Input.GetKeyDown(KeyCode.E),
        };

        if (_stats.SnapInput)
        {
            _frameInput.Move.x =
                Mathf.Abs(_frameInput.Move.x) < _stats.HorizontalDeadZoneThreshold
                    ? 0
                    : Mathf.Sign(_frameInput.Move.x);
            _frameInput.Move.y =
                Mathf.Abs(_frameInput.Move.y) < _stats.VerticalDeadZoneThreshold
                    ? 0
                    : Mathf.Sign(_frameInput.Move.y);
        }

        if (_frameInput.Move.x != 0)
            _lastFacing = _frameInput.Move.x;

        if (_frameInput.JumpDown)
        {
            _jumpToConsume = true;
            _timeJumpWasPressed = _time;
        }

        if (_frameInput.DashDown)
            _dashToConsume = true;
    }

    private void FixedUpdate()
    {
        CheckCollisions();
        HandleCrouch();
        // If in water, swim and skip dash/jump
        if (_isSwimming)
        {
            HandleSubmerging();
            HandleSwimming();
            HandleRotation();
            ApplyMovement();
            return;
        }

        if (_isClimbing)
        {
            HandleClimbing();
            ApplyMovement();
            return;
        }
        HandleDash();

        if (!_isDashing)
        {
            HandleJump();
            HandleDirection();
            HandleGravity(); // Gliding logic is now inside HandleGravity
            HandleRotation();
        }

        ApplyMovement();
    }

    #region Collisions

    private float _frameLeftGrounded = float.MinValue;
    private bool _grounded;

    private void CheckCollisions()
    {
        Physics2D.queriesStartInColliders = false;

        // Ground and Ceiling
        bool groundHit = Physics2D.CapsuleCast(
            _col.bounds.center,
            _col.size,
            _col.direction,
            0,
            Vector2.down,
            _stats.GrounderDistance,
            ~_stats.PlayerLayer
        );
        bool ceilingHit = Physics2D.CapsuleCast(
            _col.bounds.center,
            _col.size,
            _col.direction,
            0,
            Vector2.up,
            _stats.GrounderDistance,
            ~_stats.PlayerLayer
        );

        // Hit a Ceiling
        if (ceilingHit)
            _frameVelocity.y = Mathf.Min(0, _frameVelocity.y);

        // Landed on the Ground
        if (!_grounded && groundHit)
        {
            _grounded = true;
            _coyoteUsable = true;
            _bufferedJumpUsable = true;
            _endedJumpEarly = false;
            _isGliding = false; // NEW: Stop gliding on ground
            GroundedChanged?.Invoke(true, Mathf.Abs(_frameVelocity.y));
        }
        // Left the Ground
        else if (_grounded && !groundHit)
        {
            _grounded = false;
            _frameLeftGrounded = _time;
            GroundedChanged?.Invoke(false, 0);
        }

        Physics2D.queriesStartInColliders = _cachedQueryStartInColliders;
    }

    #endregion

    #region Jumping

    private bool _jumpToConsume;
    private bool _bufferedJumpUsable;
    private bool _endedJumpEarly;
    private bool _coyoteUsable;
    private float _timeJumpWasPressed;

    private bool HasBufferedJump =>
        _bufferedJumpUsable && _time < _timeJumpWasPressed + _stats.JumpBuffer;
    private bool CanUseCoyote =>
        _coyoteUsable && !_grounded && _time < _frameLeftGrounded + _stats.CoyoteTime;

    private void HandleJump()
    {
        if (!_endedJumpEarly && !_grounded && !_frameInput.JumpHeld && _rb.linearVelocity.y > 0)
            _endedJumpEarly = true;

        if (!_jumpToConsume && !HasBufferedJump)
            return;

        if (_grounded || CanUseCoyote)
            ExecuteJump();

        _jumpToConsume = false;
    }

    private void ExecuteJump()
    {
        _endedJumpEarly = false;
        _timeJumpWasPressed = 0;
        _bufferedJumpUsable = false;
        _coyoteUsable = false;
        _frameVelocity.y = _stats.JumpPower;
        Jumped?.Invoke();
    }

    #endregion

    #region Horizontal

    private void HandleDirection()
    {
        if (_isClimbing)
            return;

        if (_isGliding)
        {
            // While gliding, move forward at a set speed, ignoring horizontal input
            _frameVelocity.x = Mathf.MoveTowards(
                _frameVelocity.x,
                _lastFacing * _stats.GlideForwardSpeed,
                _stats.Acceleration * UnityEngine.Time.fixedDeltaTime
            );
        }
        else
        {
            // Regular horizontal movement
            if (_frameInput.Move.x == 0)
            {
                var deceleration = _grounded ? _stats.GroundDeceleration : _stats.AirDeceleration;
                _frameVelocity.x = Mathf.MoveTowards(
                    _frameVelocity.x,
                    0,
                    deceleration * UnityEngine.Time.fixedDeltaTime
                );
            }
            else
            {
                _frameVelocity.x = Mathf.MoveTowards(
                    _frameVelocity.x,
                    _frameInput.Move.x * _stats.MaxSpeed,
                    _stats.Acceleration * UnityEngine.Time.fixedDeltaTime
                );
            }
        }
    }

    #endregion

    #region Gravity

    // MODIFIED: This method now includes the gliding logic.
    private void HandleGravity()
    {
        // Player is airborne, falling, and holding the glide button
        if (!_grounded && _frameInput.GlideHeld && _frameVelocity.y < 0)
        {
            _isGliding = true;
            // Clamp the fall speed to the glide speed
            _frameVelocity.y = Mathf.MoveTowards(
                _frameVelocity.y,
                -_stats.GlideFallSpeed,
                _stats.FallAcceleration * UnityEngine.Time.fixedDeltaTime
            );
        }
        else
        {
            _isGliding = false;
            if (_grounded && _frameVelocity.y <= 0f)
            {
                _frameVelocity.y = _stats.GroundingForce;
            }
            else
            {
                var inAirGravity = _stats.FallAcceleration;
                if (_endedJumpEarly && _frameVelocity.y > 0)
                    inAirGravity *= _stats.JumpEndEarlyGravityModifier;
                _frameVelocity.y = Mathf.MoveTowards(
                    _frameVelocity.y,
                    -_stats.MaxFallSpeed,
                    inAirGravity * UnityEngine.Time.fixedDeltaTime
                );
            }
        }
    }

    #endregion

    private void ApplyMovement() => _rb.linearVelocity = _frameVelocity;

    private void HandleRotation()
    {
        // Determine the target angle. Tilted if gliding, upright otherwise.
        // The tilt direction depends on the last direction the player was facing.
        float targetAngle = _isGliding ? _stats.GlideTiltAngle * -_lastFacing : 0f;

        //This is to prevent the player from rotating while climbing
        if (_isClimbing)
            targetAngle = 0;

        if (_isSwimming)
        {
            // Rotate player towards movement direction
            if (_frameInput.Move.magnitude > 0.1f)
            {
                // Subtract 90 degrees to align the player's 'up' with the movement direction
                targetAngle =
                    Mathf.Atan2(_frameInput.Move.y, _frameInput.Move.x) * Mathf.Rad2Deg - 90f;
            }
        }

        // Smoothly rotate the Rigidbody towards the target angle.
        // Using LerpAngle for a more natural-looking interpolation.
        // Use _rb.MoveRotation for physics-safe rotation in FixedUpdate.
        if (_isSwimming)
        {
            float smoothedAngle = Mathf.LerpAngle(
                _rb.rotation,
                targetAngle,
                _stats.SwimRotationSpeed * UnityEngine.Time.fixedDeltaTime
            );
            _rb.MoveRotation(smoothedAngle);
        }
        else if (_isGliding)
        {
            float smoothedAngle = Mathf.LerpAngle(
                _rb.rotation,
                targetAngle,
                _stats.GlideRotationSpeed * UnityEngine.Time.fixedDeltaTime
            );
            _rb.MoveRotation(smoothedAngle);
        }
        else
        {
            _rb.MoveRotation(targetAngle);
        }

        // Set the facing direction
        if (_currentRope == null || _currentRope.Direction == RopeDirection.Both)
        {
            if (_frameInput.Move.x < 0)
            {
                _lastFacing = -1;
            }
            else if (_frameInput.Move.x > 0)
            {
                _lastFacing = 1;
            }
        }
    }

    private void HandleDash()
    {
        if (_dashToConsume && !_isDashing)
        {
            _isDashing = true;
            _dashTimeLeft = _stats.DashDuration;
            _dashTraveled = 0f;
            var dir =
                _frameInput.Move != Vector2.zero
                    ? _frameInput.Move.normalized
                    : new Vector2(_lastFacing, 0);
            _dashDirection = dir;
            _dashToConsume = false;
        }
        if (_isDashing)
        {
            _frameVelocity = _dashDirection * _stats.DashPower;
            _dashTimeLeft -= UnityEngine.Time.fixedDeltaTime;
            _dashTraveled += _stats.DashPower * UnityEngine.Time.fixedDeltaTime;
            if (_dashTimeLeft <= 0 || _dashTraveled >= _stats.DashDistance)
                _isDashing = false;
        }
    }

    /// <summary>
    /// Adjusts the capsule collider for crouch height reduction when holding X and restores on release
    /// </summary>
    private void HandleCrouch()
    {
        var stats = _stats;
        bool wantCrouch = _grounded && _frameInput.CrouchHeld;
        // Shrink collider when starting crouch
        if (wantCrouch && !_isCrouching)
        {
            _isCrouching = true;
            var newHeight = Mathf.Max(0.1f, _origColHeight - stats.CrouchHeightReduction);
            _col.size = new Vector2(_col.size.x, newHeight);
            _col.offset = _origColOffset - new Vector2(0, stats.CrouchHeightReduction / 2f);
        }
        // Restore collider when releasing crouch
        else if (!wantCrouch && _isCrouching)
        {
            _isCrouching = false;
            _col.size = new Vector2(_col.size.x, _origColHeight);
            _col.offset = _origColOffset;
        }
    }

    private void HandleSubmerging()
    {
        var stats = _stats;
        bool wantDive = _isSwimming && _frameInput.CrouchHeld;
        if (wantDive && !_isDiving)
        {
            _isDiving = true;
            var newHeight = Mathf.Max(0.1f, _origColHeight - stats.CrouchHeightReduction);
            _col.size = new Vector2(_col.size.x, newHeight);
            _col.offset = _origColOffset - new Vector2(0, stats.CrouchHeightReduction / 2f);
        }
        else if (!wantDive && _isDiving)
        {
            _isDiving = false;
            _col.size = new Vector2(_col.size.x, _origColHeight);
            _col.offset = _origColOffset;
        }
    }

    /// <summary>
    /// Swim movement: use input for 2D movement, disable dash/jump
    /// </summary>
    private void HandleSwimming()
    {
        if (_waterCollider == null)
            return;

        var move = _frameInput.Move;
        var waterSurfaceY = _waterCollider.bounds.max.y;
        var playerTopY = _col.bounds.max.y;

        // Prevent upward movement if at or above the surface
        if (playerTopY >= waterSurfaceY && move.y > 0)
        {
            move.y = 0;
        }

        // Calculate horizontal and vertical velocity based on input
        if (move.magnitude > 0.1f)
        {
            _frameVelocity = move.normalized * _stats.SwimSpeed;
        }
        else
        {
            // Apply drag when there's no input
            _frameVelocity = Vector2.MoveTowards(
                _frameVelocity,
                Vector2.zero,
                _stats.SwimDrag * UnityEngine.Time.fixedDeltaTime
            );
        }

        // Apply buoyancy only when submerged and not actively moving vertically.
        if (playerTopY < waterSurfaceY && Mathf.Abs(move.y) < 0.1f)
        {
            _frameVelocity.y = Mathf.MoveTowards(
                _frameVelocity.y,
                _stats.Buoyancy,
                _stats.FallAcceleration * UnityEngine.Time.fixedDeltaTime
            );
        }
    }

    private void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Water"))
        {
            _isSwimming = true;
            _isSubmerged = true;
            _waterCollider = other;
        }
        else if (other.CompareTag("BouncePad"))
        {
            // Only trigger when falling onto it
            if (_rb.linearVelocity.y <= -0.1f)
            {
                ExecuteBouncePad(other.GetComponent<BouncePad>());
            }
        }
    }

    public void OnEnterRopeDownZone(RopeDownInteractionZone zone)
    {
        _currentRopeZone = zone;
    }

    public void OnExitRopeDownZone(RopeDownInteractionZone zone)
    {
        if (_currentRopeZone == zone)
        {
            _currentRopeZone = null;
        }
    }

    private void ExecuteBouncePad(BouncePad pad)
    {
        if (pad == null || !pad.CanBounce)
            return;

        _frameVelocity.y = pad.BounceForce; // Apply the bounce to vertical velocity

        pad.TriggerEffects(); // Play Animation/Sound and start cooldown on pad side
    }

    private void OnTriggerExit2D(Collider2D other)
    {
        if (other.CompareTag("Water"))
        {
            _isSwimming = false;
            _isSubmerged = false;
            _waterCollider = null;
        }
    }

    private void HandleRopeLatching()
    {
        // If E is pressed and we are in the top zone...
        if (_frameInput.InteractDown && _currentRopeZone != null)
        {
            // ...latch to the top point, regardless of whether we are already climbing.
            // This allows overriding the automatic climb.
            transform.position = _currentRopeZone.GetLatchPoint();
            SetClimbing(true, _currentRopeZone.Rope);
        }
    }

    public void SetClimbing(bool climbing, Rope rope = null)
    {
        _isClimbing = climbing;
        _currentRope = rope;

        if (climbing)
        {
            _rb.linearVelocity = Vector2.zero; // Stop movement when latching
            _rb.rotation = 0;
            if (rope != null)
            {
                if (rope.Direction == RopeDirection.Left)
                {
                    _lastFacing = -1;
                }
                else if (rope.Direction == RopeDirection.Right)
                {
                    _lastFacing = 1;
                }
            }
        }
    }

    public void ExitRope(Rope rope)
    {
        if (!_isClimbing || _currentRope != rope)
            return;

        _isClimbing = false;
        _currentRope.StartCooldown(_stats.RopeCooldown);
        transform.position = _currentRope.WorldPlayerExitPoint;
        _frameVelocity = new Vector2(
            _currentRope.ExitForce.x * _lastFacing,
            _currentRope.ExitForce.y
        );
    }

    private void HandleClimbing()
    {
        if (_currentRope == null)
        {
            _isClimbing = false;
            return;
        }
        if (_jumpToConsume)
        {
            ExecuteRopeJump();
            return;
        }

        var move = _frameInput.Move;
        _frameVelocity.x = 0;
        _frameVelocity.y = move.y * _stats.ClimbSpeed;
    }

    public void ApplyVelocity(Vector2 vel)
    {
        _frameVelocity = vel;
    }

    private void ExecuteRopeJump()
    {
        _isClimbing = false;
        _frameVelocity = new Vector2(_stats.RopeJumpPower.x * _lastFacing, _stats.RopeJumpPower.y);
        _jumpToConsume = false;
        _endedJumpEarly = false;
        _timeJumpWasPressed = 0;
        _bufferedJumpUsable = false;
        _coyoteUsable = false;
        Jumped?.Invoke();
    }

    // Additional properties and methods for compatibility with existing systems
    public Vector2 FrameVelocity { get => _frameVelocity; set => _frameVelocity = value; }
    public float Time => _time;
    public float FrameLeftGrounded => _frameLeftGrounded;
    public bool WantsToClimb => _isClimbing;
    public Rope CurrentRope => _currentRope;
    public RopeDownInteractionZone CurrentRopeDownZone => _currentRopeZone;
    public Collider2D WaterCollider => _waterCollider;
    public float OrigColHeight => _origColHeight;
    public Vector2 OrigColOffset => _origColOffset;
    public CapsuleCollider2D Col => _col;
    public Rigidbody2D RB => _rb;
    public float LastFacing { get => _lastFacing; set => _lastFacing = value; }
    public bool CanControl { get; set; } = true;
    public FrameInput Input => _frameInput;
    public bool IsSubmerged => _isSubmerged;
    public bool IsCrouching { get => _isCrouching; set => _isCrouching = value; }
    public bool IsGliding { get => _isGliding; set => _isGliding = value; }

    // Methods for compatibility
    public bool IsGrounded() => _grounded;
    public void InvokeJumped() => Jumped?.Invoke();
    public void StopClimbing() => SetClimbing(false, null);
    public bool CheckForLedge() => false; // Not implemented in this version

    // IPlayerController interface methods
    public void SetVelocity(Vector2 velocity) => _rb.linearVelocity = velocity;
    public void SetVerticalVelocity(float y) => _rb.linearVelocity = new Vector2(_rb.linearVelocity.x, y);
    public void SwitchState(PlayerStateBase state) { } // Not used in this version

#if UNITY_EDITOR
    private void OnValidate()
    {
        if (_stats == null)
            Debug.LogWarning(
                "Please assign a PlayerScriptableStats asset to the Player Controller's Stats slot",
                this
            );
    }
#endif
}
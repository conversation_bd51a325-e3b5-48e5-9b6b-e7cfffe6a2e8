using System.Collections.Generic;
using UnityEngine;

public class ProjectilePool : MonoBehaviour
{
    [Tooltip("Projectile prefab to pool")]
    [SerializeField]
    private SlingshotProjectile _prefab;

    [Tooltip("Number of instances to preload into the pool")]
    [SerializeField]
    private int _poolSize = 10;

    private Queue<SlingshotProjectile> _items;

    private void Awake()
    {
        _items = new Queue<SlingshotProjectile>();
        for (int i = 0; i < _poolSize; i++)
        {
            var p = Instantiate(_prefab, transform);
            p.gameObject.SetActive(false);
            _items.Enqueue(p);
        }
    }

    /// <summary>
    /// Retrieves a projectile from the pool, activating it.
    /// </summary>
    public SlingshotProjectile Get()
    {
        if (_items.Count == 0)
        {
            // Expand pool if empty
            var extra = Instantiate(_prefab, transform);
            extra.gameObject.SetActive(false);
            _items.Enqueue(extra);
        }

        var proj = _items.Dequeue();
        // Reset transform and rigidbody to default state
        proj.transform.localScale = Vector3.one;
        proj.transform.rotation = Quaternion.identity;
        var rb = proj.GetComponent<Rigidbody2D>();
        rb.linearVelocity = Vector2.zero;
        rb.angularVelocity = 0f;
        proj.gameObject.SetActive(true);
        _items.Enqueue(proj);
        return proj;
    }

    /// <summary>
    /// Returns a projectile to the pool (call when disabling).
    /// </summary>
    public void Return(SlingshotProjectile proj)
    {
        proj.gameObject.SetActive(false);
    }
}

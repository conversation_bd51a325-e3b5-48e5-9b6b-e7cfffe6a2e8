
# Anchor Light - Coding Guidelines

This document outlines the coding conventions and best practices to be followed when working on the Anchor Light project. The goal is to maintain a clean, consistent, and maintainable codebase.

## 1. General Principles

*   **Readability:** Write clear, self-documenting code. Use meaningful names for variables, methods, and classes.
*   **Consistency:** Adhere to the existing code style and patterns.
*   **Modularity:** Keep classes and methods focused on a single responsibility.
*   **Data-Driven Design:** Utilize ScriptableObjects to separate data from logic, allowing for easier tuning and iteration.
*   **No Magic Numbers:** Avoid the use of raw, unexplained numbers (literals) directly in the code. Instead, define them as constants or variables with meaningful names.
*   **DRY (Don't Repeat Yourself):** Strive to reduce repetition of software patterns, replacing it with abstractions or using data normalization to avoid redundancy.
*   **SOLID Principles:** Adhere to the five SOLID principles of object-oriented design to create more understandable, flexible, and maintainable systems.
*   **Clean Architecture:** Follow the principles of Clean Architecture to create systems that are independent of frameworks, testable, and independent of UI, database, and external agencies.

## 2. Onboarding and Codebase Familiarization

*   **Understand the Architecture:** Before making changes, take the time to understand the overall architecture of the project. Review the existing folder structure, the state machine implementation, and how data is managed with ScriptableObjects.
*   **Familiarize Yourself with Core Systems:** Identify and study the core systems of the game, such as the player controller, input handling, and interaction systems.
*   **Read Before You Write:** Before adding new features or modifying existing ones, read the relevant code to understand its purpose, dependencies, and how it fits into the larger system.
*   **Consult the Documentation:** Refer to this document and any other available documentation to ensure that your changes align with the established conventions and best practices.

## 3. Naming Conventions

*   **Classes and Interfaces:** Use PascalCase (e.g., `PlayerController`, `IPlayerController`).
*   **Methods:** Use PascalCase (e.g., `HandleMovement`, `ExecuteJump`).
*   **Public and Serialized Fields:** Use PascalCase (e.g., `public float Speed`, `[SerializeField] private float _jumpHeight`).
*   **Private Fields:** Use camelCase with a leading underscore (e.g., `_rigidbody`, `_isGrounded`).
*   **Local Variables:** Use camelCase (e.g., `var hit`, `float horizontalInput`).
*   **Enums:** Use PascalCase for both the type and its values (e.g., `RopeDirection.Left`).

## 4. Code Structure and Formatting

*   **File Organization:** Maintain the existing folder structure. New scripts should be placed in the appropriate subfolder (e.g., `Assets/_Scripts/Game/NewFeature`).
*   **Namespaces:** Use namespaces to organize code, especially for larger systems
*   **Braces:** Use the Allman style (braces on a new line) for classes, methods, and control structures.
*   **Spacing:** Use a single space after commas and around operators.
*   **Comments:** Write clear and concise comments to explain complex logic or non-obvious code. Use `//` for single-line comments and `///` for XML documentation comments on public methods and classes.

## 5. Design Patterns

*   **State Machine:** The player controller uses a state machine pattern to manage player behavior. New player states should inherit from `PlayerStateBase` and implement the `Enter`, `Exit`, `Update`, and `FixedUpdate` methods.
*   **Interfaces:** Use interfaces to define contracts between components (e.g., `IPlayerController`, `IShootable`). This promotes loose coupling and testability.
*   **Events:** Use C# events (`Action`) for communication between components. This helps to decouple systems and avoid direct dependencies.
*   **ScriptableObjects:** Use ScriptableObjects to store and manage game data (e.g., `PlayerScriptableStats`). This allows for easy tweaking of values without modifying code.

## 6. Unity-Specific Best Practices

*   **`[RequireComponent]`:** Use the `[RequireComponent]` attribute to ensure that required components are present on a GameObject.
*   **`[SerializeField]`:** Use `[SerializeField]` to expose private fields in the Inspector instead of making them public.
*   **`OnValidate`:** Use `OnValidate` in the editor to provide warnings or feedback when required data is missing (e.g., checking for a null `ScriptableStats` asset).
*   **Gizmos:** Use `OnDrawGizmos` to provide visual feedback in the Scene view for things like trigger zones, patrol paths, and other invisible game elements.
*   **Input Handling:** Use the `FrameInput` struct to gather all player input in one place. This makes it easier to manage and debug input-related issues.

## 7. Areas for Improvement and Enforcement

*   **Error Handling:** While the current code is clean, there is a lack of explicit error handling. Consider adding null checks and other safeguards, especially when dealing with `GetComponent` or external data.
*   **Code Documentation:** While comments are used effectively, there is an opportunity to improve the XML documentation for public APIs. This will make it easier for new developers to understand how to use the existing systems.
*   **Testing:** There are no tests in the project. Consider adding unit tests for core systems and logic to ensure that new changes do not break existing functionality.
*   **Dependency Injection:** While the project uses interfaces and events to decouple systems, there is an opportunity to use a more formal dependency injection framework to manage dependencies between classes.
*   **Object Pooling:** The `ProjectilePool` is a good start, but object pooling should be used more broadly for frequently instantiated and destroyed objects (e.g., particle effects, enemies) to improve performance.

## 8. Performance Considerations

*   **Update vs. FixedUpdate:** Use `FixedUpdate` for physics-related calculations (e.g., applying forces, raycasting) to ensure consistent behavior across different frame rates. Use `Update` for input handling, animations, and other non-physics-related logic.
*   **Coroutines vs. Async/Await:** Prefer `async/await` for asynchronous operations that do not need to be tied to a `MonoBehaviour`'s lifecycle. Use coroutines for time-based or sequential operations that are easier to manage within a `MonoBehaviour`.
*   **Avoid Expensive Operations in Update:** Avoid using `GetComponent`, `FindObjectOfType`, and other expensive operations inside `Update` or `FixedUpdate`. Cache component references in `Awake` or `Start`.
*   **Profiling and Optimization:** Regularly profile the game using Unity's Profiler to identify performance bottlenecks. Optimize code and assets as needed to maintain a smooth frame rate.

## 9. Animation and Audio Guidelines

*   **Animation States and Transitions:** Use Unity's Animator Controller to manage animation states and transitions. Keep the animator logic clean and organized, and use parameters to control transitions between states.
*   **Audio Management:** Implement a centralized audio manager to handle sound playback, volume control, and audio mixing. Consider using audio pooling for frequently played sounds to reduce overhead.

## 10. UI and UX Coding Standards

*   **UI Structure:** Structure UI scripts to be modular and reusable. Separate UI logic from game logic to improve maintainability.
*   **UI Toolkit vs. Canvas:** For new UI development, consider using Unity's UI Toolkit for its performance benefits and modern workflow. For existing projects, follow best practices for the Canvas system, such as using appropriate anchors and pivots.
*   **Localization:** Design UI with localization in mind. Use text components that support localization and avoid hardcoding text directly in scripts.

## 11. Debugging and Logging

*   **Structured Logging:** Use a structured logging approach to make logs easier to read and filter. Include relevant context, such as the class and method name, in log messages.
*   **Conditional Compilation:** Use conditional compilation flags (e.g., `#if UNITY_EDITOR`) to include debug-only code that should not be included in release builds.

## 12. Post-Change Documentation

*   **Update Documentation:** After making any changes to the codebase, you must document these changes in the `Assets/_Scripts/documentation.md` file. If this file does not exist, you must create it. This ensures that the documentation is always up-to-date with the latest changes. The documentation should follow this format:
    *   **Title:** Folder or module name as the H1 heading (e.g., `# Scripts`).
    *   **Overview:** A brief description of the folder’s role in the project (2–4 sentences).
    *   **Contents Summary:** A list of key files, classes, or scripts with short descriptions.
    *   **Usage:** An explanation of how to use the folder contents or how they fit into the project workflow.
    *   **Important Notes:** Any known issues, limitations, or special instructions.
    *   **Dependencies:** A list of any dependencies inside or outside the folder.
    *   **Examples (optional):** Usage snippets or workflows if applicable.

using UnityEngine;

public class PlayerGroundState : PlayerStateBase
{
    private float _timeToClimb;
    private float _timeToSwim;
    private const float TRANSITION_DELAY = 0.1f;

    public PlayerGroundState(PlayerController player)
        : base(player) { }

    public override void Update()
    {
        CheckForTransitions();
    }

    public override void FixedUpdate()
    {
        HandleCrouch();
        HandleDirection();
        HandleGravity();
        player.ApplyMovement();
    }

    private void CheckForTransitions()
    {
        // Grounded -> Air
        if (!player.IsGrounded())
        {
            player.SwitchState(player.AirState);
            return;
        }

        // Grounded -> Dash
        if (player.Input.DashDown)
        {
            player.SwitchState(player.DashState);
            return;
        }

        // Grounded -> Jump
        if (player.Input.JumpDown)
        {
            ExecuteJump();
            player.SwitchState(player.AirState);
            return;
        }

        // Debounced transitions to swim/climb
        HandleDelayedTransitions();
    }

    private void HandleDelayedTransitions()
    {
        // Swim transition
        if (player.IsSubmerged)
        {
            _timeToSwim += Time.deltaTime;
            if (_timeToSwim >= TRANSITION_DELAY)
            {
                player.SwitchState(player.SwimState);
            }
        }
        else
        {
            _timeToSwim = 0;
        }

        // Climb transition
        if (player.CurrentRopeDownZone != null && player.Input.Move.y < 0)
        {
            _timeToClimb += Time.deltaTime;
            if (_timeToClimb >= TRANSITION_DELAY)
            {
                player.transform.position = player.CurrentRopeDownZone.GetLatchPoint();
                player.SetClimbing(true, player.CurrentRopeDownZone.Rope);
                player.SwitchState(player.ClimbState);
            }
        }
        else
        {
            _timeToClimb = 0;
        }
    }

    private void HandleCrouch()
    {
        var stats = player.Stats;
        bool wantCrouch = player.IsGrounded() && player.Input.CrouchHeld;
        if (wantCrouch && !player.IsCrouching)
        {
            player.IsCrouching = true;
            var newHeight = Mathf.Max(0.1f, player.OrigColHeight - stats.CrouchHeightReduction);
            player.Col.size = new Vector2(player.Col.size.x, newHeight);
            player.Col.offset =
                player.OrigColOffset - new Vector2(0, stats.CrouchHeightReduction / 2f);
        }
        else if (!wantCrouch && player.IsCrouching)
        {
            player.IsCrouching = false;
            player.Col.size = new Vector2(player.Col.size.x, player.OrigColHeight);
            player.Col.offset = player.OrigColOffset;
        }
    }

    private void HandleDirection()
    {
        if (player.Input.Move.x == 0)
        {
            var deceleration = player.IsGrounded()
                ? player.Stats.GroundDeceleration
                : player.Stats.AirDeceleration;
            player.FrameVelocity = new Vector2(
                Mathf.MoveTowards(player.FrameVelocity.x, 0, deceleration * Time.fixedDeltaTime),
                player.FrameVelocity.y
            );
        }
        else
        {
            player.FrameVelocity = new Vector2(
                Mathf.MoveTowards(
                    player.FrameVelocity.x,
                    player.Input.Move.x * player.Stats.MaxSpeed,
                    player.Stats.Acceleration * Time.fixedDeltaTime
                ),
                player.FrameVelocity.y
            );
        }
    }

    private void HandleGravity()
    {
        if (player.IsGrounded() && player.FrameVelocity.y <= 0f)
        {
            player.FrameVelocity = new Vector2(player.FrameVelocity.x, player.Stats.GroundingForce);
        }
        else
        {
            var inAirGravity = player.Stats.FallAcceleration;
            player.FrameVelocity = new Vector2(
                player.FrameVelocity.x,
                Mathf.MoveTowards(
                    player.FrameVelocity.y,
                    -player.Stats.MaxFallSpeed,
                    inAirGravity * Time.fixedDeltaTime
                )
            );
        }
    }

    private void ExecuteJump()
    {
        player.FrameVelocity = new Vector2(player.FrameVelocity.x, player.Stats.JumpPower);
        player.InvokeJumped();
    }
}

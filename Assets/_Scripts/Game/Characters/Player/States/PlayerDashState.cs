using UnityEngine;

public class PlayerDashState : PlayerStateBase
{
    private float _dashTimeLeft;
    private Vector2 _dashDirection;
    private Vector2 _startPos;

    public PlayerDashState(PlayerController player)
        : base(player) { }

    public override void Enter()
    {
        _dashTimeLeft = player.Stats.DashDuration;
        _startPos = player.transform.position;
        var dir =
            player.Input.Move != Vector2.zero
                ? player.Input.Move.normalized
                : new Vector2(player.LastFacing, 0);
        _dashDirection = dir;
    }

    public override void FixedUpdate()
    {
        player.FrameVelocity = _dashDirection * player.Stats.DashPower;
        _dashTimeLeft -= Time.fixedDeltaTime;

        CheckForTransitions();
    }

    private void CheckForTransitions()
    {
        float distanceTraveled = Vector2.Distance(_startPos, player.transform.position);

        // End dash conditions
        if (
            _dashTimeLeft <= 0
            || distanceTraveled >= player.Stats.DashDistance
            || player.IsSubmerged
            || player.IsClimbing
        )
        {
            if (player.IsGrounded())
            {
                player.SwitchState(player.GroundState);
            }
            else if (player.IsSubmerged)
            {
                player.SwitchState(player.SwimState);
            }
            else
            {
                player.SwitchState(player.AirState);
            }
        }
    }
}

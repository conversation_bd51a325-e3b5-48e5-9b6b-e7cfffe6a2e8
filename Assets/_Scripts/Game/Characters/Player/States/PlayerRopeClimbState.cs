using UnityEngine;

// player should be able to latch to the  rope without any key press by just
// coming close to it or jumping toward it.
public class PlayerRopeClimbState : PlayerStateBase
{
    private bool _jumpToConsume;

    public PlayerRopeClimbState(PlayerController player)
        : base(player) { }

    public override void Enter()
    {
        player.RB.linearVelocity = Vector2.zero;
        player.RB.rotation = 0;
        if (player.CurrentRope != null)
        {
            if (player.CurrentRope.Direction == RopeDirection.Left)
            {
                player.LastFacing = -1;
            }
            else if (player.CurrentRope.Direction == RopeDirection.Right)
            {
                player.LastFacing = 1;
            }
        }
    }

    public override void Update()
    {
        if (player.CurrentRopeDownZone != null)
        {
            player.transform.position = player.CurrentRopeDownZone.GetLatchPoint();
            SetClimbing(true, player.CurrentRopeDownZone.Rope);
        }

        if (player.Input.JumpDown)
        {
            _jumpToConsume = true;
        }
    }

    public override void FixedUpdate()
    {
        HandleClimbing();
        player.ApplyMovement();
        CheckForTransitions();
    }

    private void HandleClimbing()
    {
        if (player.CurrentRope == null)
        {
            player.StopClimbing();
            return;
        }
        if (_jumpToConsume)
        {
            ExecuteRopeJump();
            return;
        }

        var move = player.Input.Move;
        player.FrameVelocity = new Vector2(0, move.y * player.Stats.ClimbSpeed);
    }

    public void SetClimbing(bool climbing, Rope rope = null)
    {
        player.CurrentRope = rope;

        if (!climbing)
        {
            player.StopClimbing();
        }
    }

    public void ExitRope(Rope rope)
    {
        if (!player.IsClimbing || player.CurrentRope != rope)
            return;

        player.StopClimbing();
        player.CurrentRope.StartCooldown(player.Stats.RopeCooldown);
        player.transform.position = player.CurrentRope.WorldPlayerExitPoint;
        player.FrameVelocity = new Vector2(
            player.CurrentRope.ExitForce.x * player.LastFacing,
            player.CurrentRope.ExitForce.y
        );
        player.AirState.WallStickGraceTime = 0.2f; // Prevent sticking to walls immediately after leaving rope
    }

    private void CheckForTransitions()
    {
        if (player.CurrentRope != null)
        {
            var ropeCollider = player.CurrentRope.GetComponent<Collider2D>();
            if (ropeCollider != null && player.Col != null)
            {
                if (player.Col.bounds.center.y <= ropeCollider.bounds.min.y)
                {
                    player.StopClimbing();
                }
            }
        }

        if (!player.WantsToClimb)
        {
            player.SwitchState(player.AirState);
            return;
        }
    }

    private void ExecuteRopeJump()
    {
        player.FrameVelocity = new Vector2(
            player.Stats.RopeJumpPower.x * player.LastFacing,
            player.Stats.RopeJumpPower.y
        );
        player.StopClimbing();
        _jumpToConsume = false;
        player.InvokeJumped();
        player.AirState.WallStickGraceTime = 0.2f; // Prevent sticking to walls immediately after jumping off rope
        player.SwitchState(player.AirState);
    }
}

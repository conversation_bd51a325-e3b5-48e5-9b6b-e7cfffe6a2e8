using UnityEngine;

/// <summary>
/// Handles the particle effects for the player based on controller events.
/// </summary>
[RequireComponent(typeof(IPlayerController))]
public class PlayerVisualEffects : MonoBehaviour
{
    [Header("References")]
    [SerializeField]
    private ParticleSystem _jumpParticles;

    [SerializeField]
    private ParticleSystem _launchParticles;

    [SerializeField]
    private ParticleSystem _moveParticles;

    [SerializeField]
    private ParticleSystem _landParticles;

    private IPlayerController _player;
    private bool _grounded;
    private ParticleSystem.MinMaxGradient _currentGradient;

    private void Awake()
    {
        _player = GetComponent<IPlayerController>();
    }

    private void OnEnable()
    {
        _player.Jumped += OnJumped;
        _player.GroundedChanged += OnGroundedChanged;
        _moveParticles.Play();
    }

    private void OnDisable()
    {
        _player.Jumped -= OnJumped;
        _player.GroundedChanged -= OnGroundedChanged;
        _moveParticles.Stop();
    }

    private void Update()
    {
        if (_grounded)
            DetectGroundColor();

        var inputStrength = Mathf.Abs(_player.FrameInput.x);
        _moveParticles.transform.localScale = Vector3.MoveTowards(
            _moveParticles.transform.localScale,
            Vector3.one * inputStrength,
            2 * Time.deltaTime
        );
    }

    private void OnJumped()
    {
        if (!_grounded)
            return;

        SetColor(_jumpParticles);
        SetColor(_launchParticles);
        _jumpParticles.Play();
    }

    private void OnGroundedChanged(bool grounded, float impact)
    {
        _grounded = grounded;
        if (grounded)
        {
            DetectGroundColor();
            SetColor(_landParticles);

            _landParticles.transform.localScale = Vector3.one * Mathf.InverseLerp(0, 40, impact);
            _landParticles.Play();
            _moveParticles.Play();
        }
        else
        {
            _moveParticles.Stop();
        }
    }

    private void DetectGroundColor()
    {
        var hit = Physics2D.Raycast(transform.position, Vector3.down, 2);
        if (!hit || hit.collider.isTrigger || !hit.transform.TryGetComponent(out SpriteRenderer r))
            return;

        var color = r.color;
        _currentGradient = new ParticleSystem.MinMaxGradient(color * 0.9f, color * 1.2f);
        SetColor(_moveParticles);
    }

    private void SetColor(ParticleSystem ps)
    {
        var main = ps.main;
        main.startColor = _currentGradient;
    }
}

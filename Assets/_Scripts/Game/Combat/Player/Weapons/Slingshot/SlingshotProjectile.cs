using System.Collections;
using UnityEngine;

[RequireComponent(typeof(Rigidbody2D))]
public class SlingshotProjectile : MonoBehaviour, IShootable
{
    [Tooltip("Time before the projectile returns to pool automatically")]
    [SerializeField]
    private float _lifetime = 3f;

    private Rigidbody2D _rb;
    private ProjectilePool _pool;

    private void Awake()
    {
        _rb = GetComponent<Rigidbody2D>();
        _pool = GetComponentInParent<ProjectilePool>();
    }

    public void Fire(Vector2 direction, float speed)
    {
        gameObject.SetActive(true);
        _rb.linearVelocity = direction * speed;
        StopAllCoroutines();
        StartCoroutine(ReturnAfter(_lifetime));
    }

    private IEnumerator ReturnAfter(float delay)
    {
        yield return new WaitForSeconds(delay);
        _pool.Return(this);
    }

    private void OnCollisionEnter2D(Collision2D col)
    {
        _pool.Return(this);
    }

    public void Shoot(Vector3 direction, float force)
    {
        Fire(direction, force);
    }
}
